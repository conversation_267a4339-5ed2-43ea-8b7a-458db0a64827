{"name": "web", "version": "0.1.0", "private": true, "engines": {"node": "22.14.0"}, "packageManager": "pnpm@10.10.0", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "dependencies": {"@ai-sdk/react": "^1.2.12", "@pearl-frontend/api-client": "workspace:*", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.4", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-query": "^5.75.2", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.493.0", "next": "15.2.3", "next-themes": "^0.4.6", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.6", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.5", "use-stick-to-bottom": "^1.1.1", "uuid": "^11.1.0"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.74.7", "@types/google.accounts": "^0.0.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint-config-next": "15.2.3", "eslint-plugin-next": "^0.0.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0"}}