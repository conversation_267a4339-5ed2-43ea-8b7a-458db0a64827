import { ApiClient } from "@/api-client";

export interface Integration {
  id: string;
  name: string;
  description: string;
  integrationType: string;
  source: string;
  isActive: boolean;
}

export interface IntegrationStrategy<Extra = unknown> {
  id: string;

  useExtraData?: () => {
    data: Extra | undefined;
    isLoading: boolean;
  };

  connect: (params: {
    apiClient: ApiClient;
    extra: Extra | undefined;
    integration: Integration;
  }) => Promise<void>;

  disconnect?: (params: {
    apiClient: ApiClient;
    integration: Integration;
  }) => Promise<void>;
}
