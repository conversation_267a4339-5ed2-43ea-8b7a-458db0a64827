import { useGetGoogleCalendarAuthUrl } from "@/api-hooks/useGetGoogleCalendarAuthUrl";
import { IntegrationStrategy } from "src/integrations/types";

type Extra = { authUrl: string };

export const googleCalendarStrategy: IntegrationStrategy<Extra> = {
  id: "google-calendar",

  useExtraData: useGetGoogleCalendarAuthUrl,

  connect: ({ apiClient, extra }) =>
    new Promise<void>((resolve, reject) => {
      if (!extra?.authUrl) {
        resolve();
        return;
      }

      const url = new URL(extra.authUrl);
      const params = new URLSearchParams(url.search);

      const codeClient = google.accounts.oauth2.initCodeClient({
        client_id: params.get("client_id")!,
        scope: params.get("scope")!,
        state: params.get("state")!,
        ux_mode: "popup",
        callback: async ({ code, state }: { code: string; state: string }) => {
          try {
            await apiClient.get(
              `/workspace/google-calendar/callback?code=${encodeURIComponent(
                code,
              )}&state=${encodeURIComponent(state)}`,
            );
            resolve();
          } catch (err) {
            reject(err);
          }
        },
      });

      codeClient.requestCode();
    }),
};
