"use client";
import { useDeleteThread, useGetThreads } from "@/api-hooks";
import { MoreHorizontal } from "lucide-react";
import { usePara<PERSON>, useRouter } from "next/navigation";

import Loader from "@/web/components/loader";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/web/components/ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/web/components/ui/sidebar";
import { useSidebar } from "@/web/components/ui/sidebar";

export default function ConversationList() {
  const { isMobile } = useSidebar();
  const router = useRouter();
  const { threadId: threadIdFromParams } = useParams();
  const {
    data: threadsResponse,
    isError: isErrorThreads,
    isLoading: isLoadingThreads,
  } = useGetThreads();
  const { mutate: deleteThread } = useDeleteThread();

  const goToThread = (threadId: string) => {
    router.push(`/thread/${threadId}`);
  };

  const isThreadActive = (threadId: string) => {
    return threadIdFromParams === threadId;
  };

  const isEmpty =
    !isLoadingThreads &&
    (!threadsResponse ||
      !threadsResponse.threads ||
      threadsResponse.threads.length === 0);

  if (isEmpty) return null;

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Conversations</SidebarGroupLabel>
      {isLoadingThreads ? (
        <Loader />
      ) : (
        <SidebarMenu>
          {threadsResponse
            ? threadsResponse.threads
                .sort((a, b) => a.createdAt.localeCompare(b.createdAt))
                .map((thread) => (
                  <SidebarMenuItem key={thread.id}>
                    <SidebarMenuButton
                      onClick={() => goToThread(thread.id)}
                      isActive={isThreadActive(thread.id)}
                    >
                      {thread.createdAt.slice(2, 19)}
                    </SidebarMenuButton>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <SidebarMenuAction showOnHover>
                          <MoreHorizontal />
                          <span className="sr-only">More</span>
                        </SidebarMenuAction>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        className="w-48"
                        side={isMobile ? "bottom" : "right"}
                        align={isMobile ? "end" : "start"}
                      >
                        <DropdownMenuItem>
                          <span>Add to folder</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <span>Rename</span>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => deleteThread(thread.id)}
                        >
                          <span>Delete</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </SidebarMenuItem>
                ))
            : null}
        </SidebarMenu>
      )}
      {!isLoadingThreads && isErrorThreads ? (
        <div>Error loading threads</div>
      ) : null}
    </SidebarGroup>
  );
}
