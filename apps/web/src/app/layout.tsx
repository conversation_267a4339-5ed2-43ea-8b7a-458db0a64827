import "@/web/app/globals.css";
import { Inter } from "next/font/google";

import type { Metadata } from "next";

import Providers from "@/web/components/providers";
import { Toaster } from "@/web/components/ui/sonner";

export const metadata: Metadata = {
  title: "Pearl",
};

const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`antialiased ${inter.className}`}>
        <Providers>{children}</Providers>
        <Toaster />
      </body>
    </html>
  );
}
