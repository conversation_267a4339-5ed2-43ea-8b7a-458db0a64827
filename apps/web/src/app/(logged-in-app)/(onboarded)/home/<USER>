"use client";

import {
  useChatStream,
  useGetMemberProfileMe,
  useGetThreads,
} from "@/api-hooks";
import { AudioLines, ChevronDown, Paperclip } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";

import { Card, CardContent } from "@/web/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/web/components/ui/dropdown-menu";
import {
  AIInput,
  AIInputButton,
  AIInputTextarea,
  AIInputTools,
} from "@/web/components/ui/kibo/input";

export default function Home() {
  const router = useRouter();

  const [localThreadId, setLocalThreadId] = useState<string>(() => uuidv4());
  const [selectedModel, setSelectedModel] = useState("GPT-4");
  const { data: memberProfile } = useGetMemberProfileMe();
  const { refetch: refetchThreads } = useGetThreads();

  useEffect(() => {
    setLocalThreadId(uuidv4());
  }, [setLocalThreadId]);

  const {
    handleSubmit,
    input,
    setInput,
    status: chatStatus,
  } = useChatStream({
    threadId: localThreadId,
    onError: (error) => {
      console.error("Chat error:", error);
    },
    onFinish: async () => {
      await refetchThreads();
    },
  });

  const handleSubmitForm = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || chatStatus === "submitted") return;

    await handleSubmit(undefined, {});
    handleNewThread(localThreadId);
  };

  const handleNewThread = (threadId: string) => {
    router.push(`/thread/${threadId}`);
  };
  return (
    <div className="flex h-[calc(100vh-120px)] items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-2xl">
        <div className="mb-8 text-center">
          <h2 className="mb-2 text-2xl font-semibold">
            Good morning, {memberProfile?.firstName}
          </h2>
        </div>

        <AIInput onSubmit={handleSubmitForm}>
          <AIInputTextarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="What would you like to discuss today?"
            disabled={chatStatus === "submitted"}
            className="max-h-[200px] min-h-[80px] resize-none"
          />
          <div className="mt-2 flex w-full items-center justify-between">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button
                  type="button"
                  className="hover:bg-accent flex items-center gap-2 rounded-md border px-3 py-2 text-sm transition-colors focus:outline-none focus:ring-0"
                  disabled={chatStatus === "submitted"}
                >
                  {selectedModel}
                  <ChevronDown className="h-4 w-4" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setSelectedModel("GPT-4")}>
                  GPT-4
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSelectedModel("GPT-3.5")}>
                  GPT-3.5
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSelectedModel("Claude")}>
                  Claude
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <AIInputTools>
              <AIInputButton
                variant="default"
                disabled={chatStatus === "submitted"}
              >
                <Paperclip className="h-4 w-4" />
              </AIInputButton>
              <AIInputButton
                variant="default"
                disabled={chatStatus === "submitted"}
              >
                <AudioLines className="h-4 w-4" />
              </AIInputButton>
            </AIInputTools>
          </div>
        </AIInput>

        <div className="mt-8 flex flex-col items-center justify-center gap-4 sm:flex-row">
          <Card className="group h-[150px] w-[90%] overflow-hidden rounded-[12px] sm:h-[120px] sm:w-[210px]">
            <CardContent className="flex h-full flex-col">
              <div className="flex flex-col">
                <div className="text-[14px] font-semibold">
                  Prep my next meeting
                </div>
                <div className="text-muted-foreground text-[14px]">
                  with Acme Corp
                </div>
              </div>
              <Image
                src="/prep-meeting.svg"
                alt="Prep meeting"
                width={67}
                height={67}
                className="mr-[-24px] mt-auto h-16 self-end opacity-80 invert filter transition-transform duration-200 group-hover:-translate-x-1 group-hover:-translate-y-1 dark:filter-none"
                style={{ transform: "rotate(-15deg)" }}
              />
            </CardContent>
          </Card>
          <Card className="group h-[150px] w-[90%] overflow-hidden rounded-[12px] sm:h-[120px] sm:w-[210px]">
            <CardContent className="flex h-full flex-col">
              <div className="flex flex-col">
                <div className="text-[14px] font-semibold">
                  Draft a follow-up email
                </div>
                <div className="text-muted-foreground text-[14px]">
                  to John Doe from Acme Corp
                </div>
              </div>
              <Image
                src="/draft.svg"
                alt="Draft email"
                width={75}
                height={75}
                className="mr-[-20px] mt-[-10px] h-24 self-end opacity-90 invert filter transition-transform duration-200 group-hover:-translate-x-1 group-hover:-translate-y-1 dark:filter-none"
                style={{ transform: "rotate(15deg)" }}
              />
            </CardContent>
          </Card>
          <Card className="group h-[150px] w-[90%] overflow-hidden rounded-[12px] sm:h-[120px] sm:w-[210px]">
            <CardContent className="flex h-full flex-col">
              <div className="flex flex-col">
                <div className="text-[14px] font-semibold">
                  Update Salesforce
                </div>
                <div className="text-muted-foreground text-[14px]">
                  with collected information
                </div>
              </div>
              <Image
                src="/crm.svg"
                alt="Update Salesforce"
                width={75}
                height={75}
                className="mr-[-36px] mt-[6px] h-20 self-end opacity-80 invert filter transition-transform duration-200 group-hover:-translate-x-1 group-hover:-translate-y-1 dark:filter-none"
                style={{ transform: "rotate(-15deg)" }}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
