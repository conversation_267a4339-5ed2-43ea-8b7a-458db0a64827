import { Account, useGetAccountThreads } from "@/api-hooks";
import { useRouter } from "expo-router";
import { Spinner, Text, View } from "tamagui";

import { formatDateTime } from "@/utils/date";

type ThreadsListProps = {
  account: Account;
};

export const ThreadsList = ({ account }: ThreadsListProps) => {
  const {
    data: accountThreadsResponse,
    isLoading,
    isError,
  } = useGetAccountThreads(account.crmId);
  const router = useRouter();

  if (isLoading) {
    return <Spinner />;
  }

  if (isError) {
    return <Text color="$chatError">{"Error loading threads"}</Text>;
  }

  return (
    <View gap="$xl">
      {accountThreadsResponse?.threads
        .sort(
          (a, b) =>
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
        )
        .map((thread) => (
          <Text
            key={thread.id}
            fontFamily="$regular"
            fontSize={14}
            lineHeight={20}
            padding="$md"
            color="$grey"
            onPress={() =>
              router.push({
                pathname: "/chat",
                params: {
                  threadId: thread.id,
                  accountId: account.crmId,
                },
              })
            }
            pressStyle={{
              scale: 0.98,
            }}
          >
            {`${account.crmName} - ${formatDateTime(thread.updatedAt, "datetime")}`}
          </Text>
        ))}
    </View>
  );
};
