import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";

export const useGetGoogleCalendarAuthUrl = () => {
  const { apiClient } = useApiClient();

  return useQuery({
    queryKey: ["googleCalendarAuthUrl"],
    queryFn: async () => {
      return await apiClient.get<{ authUrl: string }>(
        "/workspace/google-calendar/auth-url",
      );
    },
  });
};
