import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";
import { Integration } from "@/web/integrations/types";

export type Integrations = {
  activeIntegrations: Integration[];
  availableIntegrations: Integration[];
};

export const useGetIntegrations = () => {
  const { apiClient } = useApiClient();

  return useQuery({
    queryKey: ["integrations"],
    queryFn: async () => {
      return await apiClient.get<Integrations>(`/workspace/integrations`);
    },
  });
};
