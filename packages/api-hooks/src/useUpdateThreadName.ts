import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";

interface UpdateThreadNameParams {
  threadId: string;
  name: string;
}

export const useUpdateThreadName = () => {
  const { apiClient } = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ threadId, name }: UpdateThreadNameParams) => {
      return await apiClient.put(`/agent/threads/${threadId}`, { name });
    },
    onSuccess: (_, { threadId }) => {
      queryClient.invalidateQueries({ queryKey: ["threads"] });
      queryClient.invalidateQueries({ queryKey: ["thread", threadId] });
    },
    onError: (error) => {
      console.error("Failed to update thread name:", error);
    },
  });
};
