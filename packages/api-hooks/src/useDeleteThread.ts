import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";

export const useDeleteThread = () => {
  const { apiClient } = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (threadId: string) => {
      return await apiClient.delete(`/agent/threads/${threadId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["threads"] });
    },
  });
};
