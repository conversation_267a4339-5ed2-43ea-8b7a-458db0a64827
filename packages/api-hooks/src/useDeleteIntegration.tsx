import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";

export const useDeleteIntegration = () => {
  const { apiClient } = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (integrationId: string) =>
      await apiClient.delete(`/workspace/${integrationId}/connection`),

    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["integrations"] });
    },
  });
};
