import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";

export type Thread = {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
};

type ThreadsResponse = {
  threads: Thread[];
};

export const useGetThreads = () => {
  const { apiClient } = useApiClient();

  return useQuery({
    queryKey: ["threads"],
    queryFn: async () => {
      return await apiClient.get<ThreadsResponse>(`/agent/threads`);
    },
  });
};
