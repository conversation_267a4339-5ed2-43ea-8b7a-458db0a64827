import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";

interface UpdateThreadCRMAccountParams {
  threadId: string;
  crm_account_id: string;
}

export const useUpdateThreadCRMAccount = () => {
  const { apiClient } = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      threadId,
      crm_account_id,
    }: UpdateThreadCRMAccountParams) => {
      return await apiClient.put(`/agent/threads/${threadId}/crm_account_id`, {
        crm_account_id,
      });
    },
    onSuccess: (_, { threadId }) => {
      queryClient.invalidateQueries({ queryKey: ["threads"] });
      queryClient.invalidateQueries({ queryKey: ["thread", threadId] });
    },
    onError: (error) => {
      console.error("Failed to update thread CRM account:", error);
    },
  });
};
