import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";

export type AccountThread = {
  id: string;
  name: string;
  accountId: string;
  createdAt: string;
  updatedAt: string;
};

type AccountThreadsResponse = {
  threads: AccountThread[];
};

export const useGetAccountThreads = (accountId?: string) => {
  const { apiClient } = useApiClient();

  return useQuery({
    queryKey: ["accountThreads", accountId],
    queryFn: async () => {
      return await apiClient.get<AccountThreadsResponse>(
        `/agent/accounts/${accountId}/threads`,
      );
    },
    enabled: !!accountId,
  });
};
